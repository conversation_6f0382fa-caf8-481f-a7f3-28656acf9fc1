//
//  HistoryIntegration.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Integration guide and wrapper for the new modern history section
/// This file shows how to integrate the new history components with the existing app structure

// MARK: - Integration with Existing TabBarView

/// Updated TabBarView integration
/// Replace the existing history tab content with ModernHistoryView
extension TabBarView {
    
    /// Modern history tab content
    var modernHistoryTab: some View {
        NavigationView {
            ModernHistoryView(historyDataManager: HistoryDataManager(sessionHistoryManager: SessionHistoryManager()))
                .navigationBarHidden(true)
        }
        .tabItem {
            VStack {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 24))
                Text("History")
                    .font(.caption)
            }
        }
        .tag(2) // Assuming history is the third tab
    }
}

// MARK: - Data Integration

/// Session type alias for compatibility with existing CompletedSession
typealias Session = CompletedSession

/// Session data extensions for the new history components
extension CompletedSession {

    /// Start time (using date property)
    var startTime: Date {
        return date
    }

    /// Breath count (using stepsCompleted)
    var breathCount: Int {
        return stepsCompleted
    }

    /// Is completed (always true for CompletedSession)
    var isCompleted: Bool {
        return true
    }

    /// Planned duration (estimated from total steps)
    var plannedDuration: TimeInterval {
        // Estimate 30 seconds per step as default planned duration
        return Double(totalSteps) * 30.0
    }

    /// Time ago string for display
    var timeAgoString: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)

        if timeInterval < 3600 { // Less than 1 hour
            let minutes = Int(timeInterval / 60)
            return "\(minutes)m ago"
        } else if timeInterval < 86400 { // Less than 1 day
            let hours = Int(timeInterval / 3600)
            return "\(hours)h ago"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)d ago"
        }
    }
}

/// SessionQuality extensions
extension SessionQuality {

    /// Progress value (0.0 to 1.0)
    var progressValue: Double {
        switch self {
        case .excellent: return 1.0
        case .good: return 0.75
        case .fair: return 0.5
        case .poor: return 0.25
        case .none: return 0.0
        }
    }
}

// MARK: - Core Data Integration

/// History data manager that integrates with existing SessionHistoryManager
class HistoryDataManager: ObservableObject {
    private let sessionHistoryManager: SessionHistoryManager

    init(sessionHistoryManager: SessionHistoryManager) {
        self.sessionHistoryManager = sessionHistoryManager
    }

    /// Fetch sessions for dashboard
    func fetchDashboardData() async -> DashboardData {
        let sessions = sessionHistoryManager.sessions
        let weeklyGoal = 7 // From user preferences - could be made configurable
        let monthlyGoal = 30 // From user preferences - could be made configurable

        return DashboardData(
            sessions: sessions,
            weeklyGoal: weeklyGoal,
            monthlyGoal: monthlyGoal
        )
    }

    /// Fetch recent sessions
    func fetchRecentSessions(limit: Int) -> [Session] {
        return Array(sessionHistoryManager.sessions.prefix(limit))
    }

    /// Calculate analytics data
    func calculateAnalytics(for timeframe: AnalyticsView.AnalyticsTimeframe) async -> AnalyticsData {
        let sessions = sessionHistoryManager.sessions
        let calendar = Calendar.current
        let now = Date()

        // Filter sessions based on timeframe
        let filteredSessions = sessions.filter { session in
            switch timeframe {
            case .week:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .weekOfYear)
            case .month:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .month)
            case .quarter:
                let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) ?? now
                return session.date >= threeMonthsAgo
            case .year:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .year)
            }
        }

        // Generate trend data
        let qualityTrend = generateQualityTrend(from: filteredSessions)
        let adherenceTrend = generateAdherenceTrend(from: filteredSessions, timeframe: timeframe)
        let pressureTrend = generatePressureTrend(from: filteredSessions)
        let durationTrend = generateDurationTrend(from: filteredSessions)

        // Generate insights and recommendations
        let insights = generateInsights(from: filteredSessions)
        let recommendations = generateRecommendations(from: filteredSessions)

        return AnalyticsData(
            qualityTrend: qualityTrend,
            adherenceTrend: adherenceTrend,
            pressureTrend: pressureTrend,
            durationTrend: durationTrend,
            insights: insights,
            recommendations: recommendations
        )
    }

    // MARK: - Private Helper Methods

    private func generateQualityTrend(from sessions: [Session]) -> [AnalyticsDataPoint] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: sessions) { session in
            calendar.startOfDay(for: session.date)
        }

        return grouped.map { date, sessions in
            let averageQuality = sessions.reduce(0.0) { sum, session in
                sum + session.quality.progressValue
            } / Double(sessions.count)

            return AnalyticsDataPoint(date: date, value: averageQuality * 100)
        }.sorted { $0.date < $1.date }
    }

    private func generateAdherenceTrend(from sessions: [Session], timeframe: AnalyticsView.AnalyticsTimeframe) -> [AnalyticsDataPoint] {
        let calendar = Calendar.current
        let now = Date()

        // Group by week for adherence tracking
        let grouped = Dictionary(grouping: sessions) { session in
            calendar.dateInterval(of: .weekOfYear, for: session.date)?.start ?? session.date
        }

        return grouped.map { weekStart, sessions in
            // Calculate adherence rate (assuming 7 sessions per week as goal)
            let adherenceRate = min(Double(sessions.count) / 7.0, 1.0) * 100
            return AnalyticsDataPoint(date: weekStart, value: adherenceRate)
        }.sorted { $0.date < $1.date }
    }

    private func generatePressureTrend(from sessions: [Session]) -> [AnalyticsDataPoint] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: sessions) { session in
            calendar.startOfDay(for: session.date)
        }

        return grouped.map { date, sessions in
            let averagePressure = sessions.reduce(0.0) { sum, session in
                sum + session.averagePressure
            } / Double(sessions.count)

            return AnalyticsDataPoint(date: date, value: averagePressure)
        }.sorted { $0.date < $1.date }
    }

    private func generateDurationTrend(from sessions: [Session]) -> [AnalyticsDataPoint] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: sessions) { session in
            calendar.startOfDay(for: session.date)
        }

        return grouped.map { date, sessions in
            let averageDuration = sessions.reduce(0.0) { sum, session in
                sum + session.duration
            } / Double(sessions.count)

            return AnalyticsDataPoint(date: date, value: averageDuration / 60.0) // Convert to minutes
        }.sorted { $0.date < $1.date }
    }

    private func generateInsights(from sessions: [Session]) -> [KeyInsight] {
        guard !sessions.isEmpty else { return [] }

        var insights: [KeyInsight] = []

        // Quality insight
        let averageQuality = sessions.reduce(0.0) { sum, session in
            sum + session.quality.progressValue
        } / Double(sessions.count)

        if averageQuality >= 0.8 {
            insights.append(KeyInsight(
                title: "Excellent Quality",
                description: "Your session quality is consistently high",
                icon: "star.fill",
                color: .green,
                trend: "\(Int(averageQuality * 100))%",
                trendColor: .green
            ))
        }

        // Adherence insight
        let calendar = Calendar.current
        let thisWeek = sessions.filter { calendar.isDate($0.date, equalTo: Date(), toGranularity: .weekOfYear) }
        let adherenceRate = Double(thisWeek.count) / 7.0

        if adherenceRate >= 0.8 {
            insights.append(KeyInsight(
                title: "Great Adherence",
                description: "You're staying consistent with your therapy schedule",
                icon: "calendar.badge.checkmark",
                color: .green,
                trend: "\(Int(adherenceRate * 100))%",
                trendColor: .green
            ))
        }

        return insights
    }

    private func generateRecommendations(from sessions: [Session]) -> [Recommendation] {
        var recommendations: [Recommendation] = []

        // Analyze pressure patterns
        let lowPressureSessions = sessions.filter { $0.averagePressure < 10.0 }
        if Double(lowPressureSessions.count) / Double(sessions.count) > 0.3 {
            recommendations.append(Recommendation(
                title: "Increase Pressure",
                description: "Consider increasing your exhalation effort to reach the optimal pressure zone",
                priority: .medium
            ))
        }

        // Analyze session frequency
        let calendar = Calendar.current
        let thisWeek = sessions.filter { calendar.isDate($0.date, equalTo: Date(), toGranularity: .weekOfYear) }
        if thisWeek.count < 5 {
            recommendations.append(Recommendation(
                title: "Increase Frequency",
                description: "Try to complete more sessions this week to improve your therapy outcomes",
                priority: .high
            ))
        }

        return recommendations
    }
}

/// Dashboard data structure
struct DashboardData {
    let sessions: [Session]
    let weeklyGoal: Int
    let monthlyGoal: Int
    
    var weeklyCompletedSessions: Int {
        let calendar = Calendar.current
        let weekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: Date()) ?? Date()
        return sessions.filter { $0.startTime >= weekAgo && $0.isCompleted }.count
    }
    
    var monthlyCompletedSessions: Int {
        let calendar = Calendar.current
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: Date()) ?? Date()
        return sessions.filter { $0.startTime >= monthAgo && $0.isCompleted }.count
    }
    
    var currentStreak: Int {
        // Calculate current consecutive days with completed sessions
        let calendar = Calendar.current
        var streak = 0
        var currentDate = calendar.startOfDay(for: Date())
        
        while true {
            let hasSessionOnDate = sessions.contains { session in
                calendar.isDate(session.startTime, inSameDayAs: currentDate) && session.isCompleted
            }
            
            if hasSessionOnDate {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }
        
        return streak
    }
    
    var averageQuality: Double {
        let qualitySessions = sessions.filter { $0.quality != .none }
        guard !qualitySessions.isEmpty else { return 0 }
        
        let totalQuality = qualitySessions.reduce(0.0) { sum, session in
            sum + session.quality.progressValue
        }
        
        return totalQuality / Double(qualitySessions.count)
    }
}

/// Analytics data structure
struct AnalyticsData {
    let qualityTrend: [AnalyticsDataPoint]
    let adherenceTrend: [AnalyticsDataPoint]
    let pressureTrend: [AnalyticsDataPoint]
    let durationTrend: [AnalyticsDataPoint]
    let insights: [KeyInsight]
    let recommendations: [Recommendation]
}

// MARK: - Migration Guide

/// Migration steps from old history to new history
enum HistoryMigrationSteps {
    
    /// Step 1: Replace the history tab in TabBarView
    static let replaceHistoryTab = """
    In TabBarView.swift, replace the existing history tab content:
    
    // Old code:
    .tabItem {
        Image(systemName: "clock")
        Text("History")
    }
    
    // New code:
    ModernHistoryView()
        .tabItem {
            VStack {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 24))
                Text("History")
                    .font(.caption)
            }
        }
    """
    
    /// Step 2: Update data models
    static let updateDataModels = """
    Add the following extensions to your Session and SessionQuality models:
    - qualityColor computed property
    - formattedDuration computed property
    - timeAgoString computed property
    - completionPercentage computed property
    """
    
    /// Step 3: Integrate with Core Data
    static let integrateCoreData = """
    Update HistoryDataManager to work with your existing Core Data stack:
    - Replace placeholder fetch methods with actual NSFetchRequest queries
    - Integrate with existing session management
    - Update analytics calculations
    """
    
    /// Step 4: Test accessibility
    static let testAccessibility = """
    Test the new history section with:
    - VoiceOver enabled
    - Reduce Motion enabled
    - High Contrast enabled
    - Voice Control enabled
    - Different text sizes
    """
    
    /// Step 5: Performance testing
    static let performanceTesting = """
    Test performance with:
    - Large datasets (1000+ sessions)
    - Memory usage monitoring
    - Animation performance
    - Scroll performance
    - Loading times
    """
}

// MARK: - Configuration

/// Configuration for the new history section
struct HistoryConfiguration {
    
    /// Animation settings
    struct Animations {
        static let defaultDuration: TimeInterval = 0.3
        static let springResponse: Double = 0.3
        static let springDamping: Double = 0.7
        static let progressAnimationDuration: TimeInterval = 1.2
        static let shimmerDuration: TimeInterval = 2.0
    }
    
    /// Color scheme
    struct Colors {
        static let primary = Color.cyan
        static let secondary = Color.blue
        static let success = Color.green
        static let warning = Color.orange
        static let error = Color.red
        static let background = LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    /// Layout settings
    struct Layout {
        static let cardCornerRadius: CGFloat = 16
        static let buttonCornerRadius: CGFloat = 12
        static let progressRingSize: CGFloat = 120
        static let progressRingLineWidth: CGFloat = 8
        static let minimumTouchTarget: CGFloat = 44
        static let standardPadding: CGFloat = 16
        static let largePadding: CGFloat = 20
    }
    
    /// Performance settings
    struct Performance {
        static let maxCachedSessions = 100
        static let lazyLoadingThreshold = 50
        static let debounceDelay: TimeInterval = 0.5
        static let maxAnimationDuration: TimeInterval = 0.3
    }
}

// MARK: - Preview Helpers

#if DEBUG
/// Mock data for previews
extension Session {
    static var mockSessions: [Session] {
        let calendar = Calendar.current
        let now = Date()
        
        return (0..<20).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { return nil }
            
            return Session(
                id: UUID(),
                startTime: date,
                duration: Double.random(in: 300...900), // 5-15 minutes
                breathCount: Int.random(in: 20...60),
                averagePressure: Double.random(in: 10...20),
                quality: SessionQuality.allCases.randomElement() ?? .good,
                isCompleted: Bool.random(),
                plannedDuration: 600 // 10 minutes
            )
        }
    }
}

/// Mock session quality cases
extension SessionQuality: CaseIterable {
    public static var allCases: [SessionQuality] {
        return [.excellent, .good, .fair, .poor, .none]
    }
}
#endif

#Preview {
    let mockHistoryManager = SessionHistoryManager()
    let mockDataManager = HistoryDataManager(sessionHistoryManager: mockHistoryManager)
    return ModernHistoryView(historyDataManager: mockDataManager)
}
