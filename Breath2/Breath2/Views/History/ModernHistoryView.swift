//
//  ModernHistoryView.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Modern, redesigned history view with Magic UI-inspired design
struct ModernHistoryView: View {
    @State private var selectedTab: HistoryTab = .dashboard
    @State private var showingSessionDetail = false
    @State private var selectedSession: Session?
    @Namespace private var tabAnimation
    
    enum HistoryTab: String, CaseIterable {
        case dashboard = "Dashboard"
        case timeline = "Timeline"
        case analytics = "Analytics"
        
        var icon: String {
            switch self {
            case .dashboard: return "chart.pie.fill"
            case .timeline: return "clock.fill"
            case .analytics: return "chart.line.uptrend.xyaxis"
            }
        }
        
        var color: Color {
            switch self {
            case .dashboard: return .cyan
            case .timeline: return .green
            case .analytics: return .purple
            }
        }
    }
    
    var body: some View {
        ZStack {
            // Background gradient
            backgroundGradient
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Custom tab bar
                customTabBar
                
                // Content
                TabView(selection: $selectedTab) {
                    HistoryDashboard()
                        .tag(HistoryTab.dashboard)
                    
                    SessionTimeline()
                        .tag(HistoryTab.timeline)
                    
                    AnalyticsView()
                        .tag(HistoryTab.analytics)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.3), value: selectedTab)
            }
        }
        .sheet(isPresented: $showingSessionDetail) {
            if let session = selectedSession {
                SessionDetailView(session: session)
            }
        }
    }
    
    // MARK: - Custom Tab Bar
    
    private var customTabBar: some View {
        HStack(spacing: 0) {
            ForEach(HistoryTab.allCases, id: \.self) { tab in
                TabBarButton(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    namespace: tabAnimation
                ) {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selectedTab = tab
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.1),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.2),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: Color.black.opacity(0.1),
                    radius: 20,
                    x: 0,
                    y: 10
                )
        )
        .padding(.horizontal)
        .padding(.top, 10)
    }
    
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
}

/// Custom tab bar button with Magic UI styling
struct TabBarButton: View {
    let tab: ModernHistoryView.HistoryTab
    let isSelected: Bool
    let namespace: Namespace.ID
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                // Icon with animated background
                ZStack {
                    if isSelected {
                        Circle()
                            .fill(tab.color.opacity(0.2))
                            .frame(width: 32, height: 32)
                            .matchedGeometryEffect(id: "selectedBackground", in: namespace)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                    }
                    
                    Image(systemName: tab.icon)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(isSelected ? tab.color : .white.opacity(0.6))
                        .scaleEffect(isSelected ? 1.1 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                }
                
                // Text label (only show when selected)
                if isSelected {
                    AnimationHelpers.MorphingText(
                        tab.rawValue,
                        font: .system(size: 16, weight: .semibold),
                        color: tab.color
                    )
                    .transition(.opacity.combined(with: .scale))
                }
            }
            .padding(.horizontal, isSelected ? 16 : 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? tab.color.opacity(0.1) : Color.clear)
                    .overlay(
                        Capsule()
                            .stroke(
                                isSelected ? tab.color.opacity(0.3) : Color.clear,
                                lineWidth: 1
                            )
                    )
                    .scaleEffect(isPressed ? 0.95 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: isPressed)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {}
        )
        .magneticHover(strength: 5)
        .rippleEffect()
    }
}

/// Loading state for history view
struct HistoryLoadingView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header skeleton
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        LoadingSkeleton(height: 32, cornerRadius: 8)
                            .frame(width: 200)
                        
                        LoadingSkeleton(height: 16, cornerRadius: 4)
                            .frame(width: 150)
                    }
                    
                    Spacer()
                    
                    LoadingSkeleton(height: 40, cornerRadius: 20)
                        .frame(width: 40)
                }
                
                // Progress ring skeleton
                VStack(spacing: 16) {
                    LoadingSkeleton(height: 20, cornerRadius: 4)
                        .frame(width: 120)
                    
                    Circle()
                        .stroke(Color.white.opacity(0.1), lineWidth: 8)
                        .frame(width: 140, height: 140)
                        .shimmer()
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.05))
                )
                
                // Metrics grid skeleton
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    ForEach(0..<4, id: \.self) { _ in
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                LoadingSkeleton(height: 20, cornerRadius: 10)
                                    .frame(width: 20)
                                
                                Spacer()
                                
                                LoadingSkeleton(height: 16, cornerRadius: 8)
                                    .frame(width: 40)
                            }
                            
                            LoadingSkeleton(height: 32, cornerRadius: 8)
                                .frame(width: 80)
                            
                            LoadingSkeleton(height: 16, cornerRadius: 4)
                                .frame(width: 100)
                        }
                        .padding(20)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.05))
                        )
                    }
                }
                
                // Sessions list skeleton
                VStack(alignment: .leading, spacing: 16) {
                    LoadingSkeleton(height: 20, cornerRadius: 4)
                        .frame(width: 150)
                    
                    VStack(spacing: 12) {
                        ForEach(0..<3, id: \.self) { _ in
                            HStack(spacing: 16) {
                                Circle()
                                    .fill(Color.white.opacity(0.1))
                                    .frame(width: 50, height: 50)
                                    .shimmer()
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    LoadingSkeleton(height: 16, cornerRadius: 4)
                                        .frame(width: 120)
                                    
                                    LoadingSkeleton(height: 14, cornerRadius: 4)
                                        .frame(width: 80)
                                }
                                
                                Spacer()
                                
                                LoadingSkeleton(height: 12, cornerRadius: 6)
                                    .frame(width: 12)
                            }
                            .padding(16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white.opacity(0.05))
                            )
                        }
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.06, green: 0.06, blue: 0.10)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
}

/// Error state for history view
struct HistoryErrorView: View {
    let error: Error
    let retryAction: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // Error icon with breathing animation
            ZStack {
                Circle()
                    .fill(Color.red.opacity(0.1))
                    .frame(width: 100, height: 100)
                    .breathing(duration: 2.0, scaleRange: 0.9...1.1)
                
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(.red)
            }
            
            VStack(spacing: 12) {
                Text("Unable to Load History")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white.opacity(0.9))
                
                Text("There was a problem loading your session history. Please check your connection and try again.")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // Retry button with shimmer effect
            Button(action: retryAction) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("Try Again")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(Color.cyan)
                        .shadow(
                            color: Color.cyan.opacity(0.3),
                            radius: 15,
                            x: 0,
                            y: 8
                        )
                )
            }
            .magneticHover()
            .rippleEffect()
        }
        .padding(40)
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.06, green: 0.06, blue: 0.10)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
}

#Preview {
    ModernHistoryView()
}
