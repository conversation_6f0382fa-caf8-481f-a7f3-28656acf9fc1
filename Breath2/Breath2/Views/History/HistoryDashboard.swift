//
//  HistoryDashboard.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// View model for the history dashboard
@MainActor
class HistoryViewModel: ObservableObject {
    @Published var weeklyCompletedSessions = 0
    @Published var weeklyGoal = 7
    @Published var monthlyCompletedSessions = 0
    @Published var monthlyGoal = 30
    @Published var currentStreak = 0
    @Published var longestStreak = 0
    @Published var averageQuality = 0.0
    @Published var totalSessions = 0
    @Published var totalTimeSpent: TimeInterval = 0
    @Published var timeSpentTrend = 0.0
    @Published var recentSessions: [Session] = []
    @Published var hasRecentAchievement = false

    var totalTimeFormatted: String {
        let hours = Int(totalTimeSpent / 3600)
        let minutes = Int((totalTimeSpent.truncatingRemainder(dividingBy: 3600)) / 60)

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    func loadData() {
        // Load data from Core Data or other persistence layer
        // This is a placeholder implementation
        weeklyCompletedSessions = 5
        currentStreak = 8
        longestStreak = 15
        averageQuality = 0.82
        totalSessions = 45
        totalTimeSpent = 2700 // 45 minutes
        timeSpentTrend = 12.5
        hasRecentAchievement = currentStreak > 7

        // Load recent sessions
        recentSessions = [] // Load from persistence
    }

    func refreshData() async {
        // Refresh data from server or recalculate
        loadData()
    }

    func qualityTrendData(for timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        // Generate trend data based on timeframe
        // This is a placeholder implementation
        let calendar = Calendar.current
        let now = Date()
        var dataPoints: [TrendDataPoint] = []

        let dayCount: Int
        switch timeframe {
        case .week: dayCount = 7
        case .month: dayCount = 30
        case .quarter: dayCount = 90
        case .year: dayCount = 365
        }

        for i in 0..<min(dayCount, 30) {
            if let date = calendar.date(byAdding: .day, value: -i, to: now) {
                let value = Double.random(in: 60...95)
                dataPoints.append(TrendDataPoint(date: date, value: value))
            }
        }

        return dataPoints.reversed()
    }

    func adherenceTrendData(for timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        // Generate adherence trend data
        // This is a placeholder implementation
        let calendar = Calendar.current
        let now = Date()
        var dataPoints: [TrendDataPoint] = []

        let weekCount: Int
        switch timeframe {
        case .week: weekCount = 1
        case .month: weekCount = 4
        case .quarter: weekCount = 12
        case .year: weekCount = 52
        }

        for i in 0..<min(weekCount, 12) {
            if let date = calendar.date(byAdding: .weekOfYear, value: -i, to: now) {
                let value = Double.random(in: 40...100)
                dataPoints.append(TrendDataPoint(date: date, value: value))
            }
        }

        return dataPoints.reversed()
    }
}

/// Modern, minimal history dashboard with Magic UI-inspired design
struct HistoryDashboard: View {
    @StateObject private var viewModel = HistoryViewModel()
    @State private var selectedTimeframe: TrendChart.TrendTimeframe = .week
    @State private var showingSessionDetail = false
    @State private var selectedSession: Session?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Header with animated title
                    headerSection
                    
                    // Weekly progress overview
                    weeklyProgressSection
                    
                    // Key metrics grid
                    metricsGridSection
                    
                    // Trend analysis
                    trendsSection
                    
                    // Recent sessions
                    recentSessionsSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100) // Account for tab bar
            }
            .background(backgroundGradient)
            .navigationBarHidden(true)
            .refreshable {
                await viewModel.refreshData()
            }
        }
        .sheet(isPresented: $showingSessionDetail) {
            if let session = selectedSession {
                SessionDetailView(session: session)
            }
        }
        .onAppear {
            viewModel.loadData()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                // Animated greeting with sparkles
                HStack(spacing: 8) {
                    Text("Your Progress")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.white, .white.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    // Sparkle animation for achievements
                    if viewModel.hasRecentAchievement {
                        Image(systemName: "sparkles")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.yellow, .orange],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: viewModel.hasRecentAchievement)
                    }
                }
                
                Text(motivationalMessage)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            // Profile/Settings button
            Button(action: {}) {
                Image(systemName: "person.circle")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.top, 20)
    }
    
    // MARK: - Weekly Progress Section
    
    private var weeklyProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("This Week")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Text("Goal: \(viewModel.weeklyGoal) sessions")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            WeeklyAdherenceRing(
                completedSessions: viewModel.weeklyCompletedSessions,
                plannedSessions: viewModel.weeklyGoal,
                currentStreak: viewModel.currentStreak
            )
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.08),
                            Color.white.opacity(0.04)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.cyan.opacity(0.3),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: Color.cyan.opacity(0.1),
                    radius: 15,
                    x: 0,
                    y: 8
                )
        )
    }
    
    // MARK: - Metrics Grid Section
    
    private var metricsGridSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            SessionMetricCard(
                completedSessions: viewModel.monthlyCompletedSessions,
                totalSessions: viewModel.monthlyGoal,
                timeframe: "This Month"
            )
            
            AverageQualityCard(
                averageQuality: viewModel.averageQuality,
                sessionsCount: viewModel.totalSessions
            )
            
            StreakCard(
                currentStreak: viewModel.currentStreak,
                longestStreak: viewModel.longestStreak
            )
            
            MagicMetricCard(
                title: "Total Time",
                value: viewModel.totalTimeFormatted,
                subtitle: "All sessions",
                icon: "clock.fill",
                color: .purple,
                trend: viewModel.timeSpentTrend > 0 ? .up : viewModel.timeSpentTrend < 0 ? .down : .neutral,
                trendValue: viewModel.timeSpentTrend != 0 ? "\(abs(Int(viewModel.timeSpentTrend)))%" : nil
            )
        }
    }
    
    // MARK: - Trends Section
    
    private var trendsSection: some View {
        VStack(spacing: 16) {
            // Timeframe selector
            HStack {
                Text("Trends")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(TrendChart.TrendTimeframe.allCases, id: \.self) { timeframe in
                        Text(timeframe.rawValue)
                            .tag(timeframe)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }
            
            // Quality trend chart
            TrendChart(
                dataPoints: viewModel.qualityTrendData(for: selectedTimeframe),
                title: "Session Quality",
                color: .cyan,
                timeframe: selectedTimeframe
            )
            
            // Adherence trend chart
            TrendChart(
                dataPoints: viewModel.adherenceTrendData(for: selectedTimeframe),
                title: "Weekly Adherence",
                color: .green,
                timeframe: selectedTimeframe
            )
        }
    }
    
    // MARK: - Recent Sessions Section
    
    private var recentSessionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Sessions")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full session history
                }
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cyan)
            }
            
            if viewModel.recentSessions.isEmpty {
                EmptySessionsView()
            } else {
                AnimatedSessionList(
                    sessions: Array(viewModel.recentSessions.prefix(5)),
                    onSessionTap: { session in
                        selectedSession = session
                        showingSessionDetail = true
                    }
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    private var motivationalMessage: String {
        let adherenceRate = Double(viewModel.weeklyCompletedSessions) / Double(max(viewModel.weeklyGoal, 1))
        
        if adherenceRate >= 1.0 {
            return "Excellent work! You've exceeded your weekly goal 🎉"
        } else if adherenceRate >= 0.8 {
            return "Great progress! You're almost at your weekly goal 💪"
        } else if adherenceRate >= 0.5 {
            return "Keep going! You're halfway to your weekly goal 🌟"
        } else if viewModel.currentStreak > 0 {
            return "Nice streak! Keep the momentum going 🔥"
        } else {
            return "Ready to start your therapy journey? 🌱"
        }
    }
}

#Preview {
    HistoryDashboard()
}
