//
//  TrendChart.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI
import Charts

/// Magic UI-inspired trend chart for displaying session progress over time
struct TrendChart: View {
    let dataPoints: [TrendDataPoint]
    let title: String
    let color: Color
    let showGradient: Bool
    let timeframe: TrendTimeframe
    
    @State private var animateChart = false
    @State private var selectedPoint: TrendDataPoint?
    
    enum TrendTimeframe: String, CaseIterable {
        case week = "7D"
        case month = "30D"
        case quarter = "3M"
        case year = "1Y"
        
        var displayName: String {
            switch self {
            case .week: return "This Week"
            case .month: return "This Month"
            case .quarter: return "3 Months"
            case .year: return "This Year"
            }
        }
    }
    
    init(
        dataPoints: [TrendDataPoint],
        title: String,
        color: Color = .cyan,
        showGradient: Bool = true,
        timeframe: TrendTimeframe = .week
    ) {
        self.dataPoints = dataPoints
        self.title = title
        self.color = color
        self.showGradient = showGradient
        self.timeframe = timeframe
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white.opacity(0.9))
                    
                    Text(timeframe.displayName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
                
                Spacer()
                
                // Trend indicator
                if let trendValue = calculateTrend() {
                    HStack(spacing: 4) {
                        Image(systemName: trendValue > 0 ? "arrow.up.right" : trendValue < 0 ? "arrow.down.right" : "minus")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(trendValue > 0 ? .green : trendValue < 0 ? .red : .gray)
                        
                        Text("\(abs(Int(trendValue)))%")
                            .font(.system(size: 12, weight: .semibold, design: .rounded))
                            .foregroundColor(trendValue > 0 ? .green : trendValue < 0 ? .red : .gray)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill((trendValue > 0 ? Color.green : trendValue < 0 ? Color.red : Color.gray).opacity(0.1))
                            .overlay(
                                Capsule()
                                    .stroke((trendValue > 0 ? Color.green : trendValue < 0 ? Color.red : Color.gray).opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
            
            // Chart
            if !dataPoints.isEmpty {
                Chart(dataPoints) { point in
                    // Area gradient
                    if showGradient {
                        AreaMark(
                            x: .value("Date", point.date),
                            y: .value("Value", animateChart ? point.value : 0)
                        )
                        .foregroundStyle(
                            LinearGradient(
                                colors: [
                                    color.opacity(0.3),
                                    color.opacity(0.1),
                                    Color.clear
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                    }
                    
                    // Line
                    LineMark(
                        x: .value("Date", point.date),
                        y: .value("Value", animateChart ? point.value : 0)
                    )
                    .foregroundStyle(color)
                    .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                    
                    // Points
                    PointMark(
                        x: .value("Date", point.date),
                        y: .value("Value", animateChart ? point.value : 0)
                    )
                    .foregroundStyle(color)
                    .symbolSize(selectedPoint?.id == point.id ? 100 : 50)
                    .opacity(selectedPoint?.id == point.id ? 1.0 : 0.8)
                }
                .frame(height: 200)
                .chartBackground { chartProxy in
                    // Grid lines
                    Rectangle()
                        .fill(Color.clear)
                        .overlay(
                            VStack {
                                ForEach(0..<5) { i in
                                    Rectangle()
                                        .fill(Color.white.opacity(0.05))
                                        .frame(height: 1)
                                    if i < 4 { Spacer() }
                                }
                            }
                        )
                }
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: timeframe == .week ? 1 : 7)) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0))
                        AxisTick(stroke: StrokeStyle(lineWidth: 0))
                        AxisValueLabel {
                            if let date = value.as(Date.self) {
                                Text(formatAxisDate(date))
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white.opacity(0.6))
                            }
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .trailing) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0))
                        AxisTick(stroke: StrokeStyle(lineWidth: 0))
                        AxisValueLabel {
                            if let intValue = value.as(Double.self) {
                                Text("\(Int(intValue))")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white.opacity(0.6))
                            }
                        }
                    }
                }
                .chartAngleSelection(value: .constant(nil))
                .onAppear {
                    withAnimation(.easeInOut(duration: 1.5)) {
                        animateChart = true
                    }
                }
                
                // Selected point details
                if let selectedPoint = selectedPoint {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(formatSelectedDate(selectedPoint.date))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white.opacity(0.6))
                            
                            Text("\(Int(selectedPoint.value))")
                                .font(.system(size: 16, weight: .bold, design: .rounded))
                                .foregroundColor(color)
                        }
                        
                        Spacer()
                    }
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(color.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .transition(.opacity.combined(with: .scale))
                }
            } else {
                // Empty state
                VStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 32, weight: .light))
                        .foregroundColor(.white.opacity(0.4))
                    
                    Text("No data available")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
                .frame(height: 200)
                .frame(maxWidth: .infinity)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.05),
                            Color.white.opacity(0.02)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    color.opacity(0.2),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: color.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
    }
    
    private func calculateTrend() -> Double? {
        guard dataPoints.count >= 2 else { return nil }
        
        let recent = dataPoints.suffix(3).map(\.value)
        let previous = dataPoints.prefix(dataPoints.count - 3).suffix(3).map(\.value)
        
        guard !recent.isEmpty && !previous.isEmpty else { return nil }
        
        let recentAvg = recent.reduce(0, +) / Double(recent.count)
        let previousAvg = previous.reduce(0, +) / Double(previous.count)
        
        guard previousAvg > 0 else { return nil }
        
        return ((recentAvg - previousAvg) / previousAvg) * 100
    }
    
    private func formatAxisDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        switch timeframe {
        case .week:
            formatter.dateFormat = "E"
        case .month:
            formatter.dateFormat = "d"
        case .quarter, .year:
            formatter.dateFormat = "MMM"
        }
        return formatter.string(from: date)
    }
    
    private func formatSelectedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

/// Data point for trend charts
struct TrendDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
}

#Preview {
    ScrollView {
        VStack(spacing: 20) {
            TrendChart(
                dataPoints: [
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -6, to: Date())!, value: 12),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -5, to: Date())!, value: 15),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -4, to: Date())!, value: 18),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -3, to: Date())!, value: 14),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -2, to: Date())!, value: 20),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -1, to: Date())!, value: 22),
                    TrendDataPoint(date: Date(), value: 25)
                ],
                title: "Session Quality",
                color: .cyan
            )
            
            TrendChart(
                dataPoints: [],
                title: "Weekly Progress",
                color: .green
            )
        }
        .padding()
    }
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
