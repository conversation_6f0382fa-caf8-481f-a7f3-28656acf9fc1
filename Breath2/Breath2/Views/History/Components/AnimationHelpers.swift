//
//  AnimationHelpers.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Magic UI-inspired animation helpers and modifiers
struct AnimationHelpers {
    
    /// Shimmer effect for loading states
    struct ShimmerEffect: ViewModifier {
        @State private var phase: CGFloat = 0
        let duration: Double
        let bounce: Bool
        
        init(duration: Double = 2.0, bounce: Bool = false) {
            self.duration = duration
            self.bounce = bounce
        }
        
        func body(content: Content) -> some View {
            content
                .overlay(
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.clear,
                                    Color.white.opacity(0.3),
                                    Color.clear
                                ],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .rotationEffect(.degrees(30))
                        .offset(x: phase)
                        .clipped()
                )
                .onAppear {
                    withAnimation(
                        .linear(duration: duration)
                        .repeatForever(autoreverses: bounce)
                    ) {
                        phase = 300
                    }
                }
        }
    }
    
    /// Blur fade transition effect
    struct BlurFade: ViewModifier {
        let isVisible: Bool
        let duration: Double
        let delay: Double
        
        init(isVisible: Bool, duration: Double = 0.6, delay: Double = 0) {
            self.isVisible = isVisible
            self.duration = duration
            self.delay = delay
        }
        
        func body(content: Content) -> some View {
            content
                .opacity(isVisible ? 1 : 0)
                .blur(radius: isVisible ? 0 : 10)
                .scaleEffect(isVisible ? 1 : 0.8)
                .animation(
                    .easeOut(duration: duration).delay(delay),
                    value: isVisible
                )
        }
    }
    
    /// Morphing text effect
    struct MorphingText: View {
        let text: String
        let font: Font
        let color: Color
        
        @State private var animatedText = ""
        @State private var currentIndex = 0
        
        init(_ text: String, font: Font = .body, color: Color = .primary) {
            self.text = text
            self.font = font
            self.color = color
        }
        
        var body: some View {
            Text(animatedText)
                .font(font)
                .foregroundColor(color)
                .onAppear {
                    animateText()
                }
                .onChange(of: text) { _, newValue in
                    animatedText = ""
                    currentIndex = 0
                    animateText()
                }
        }
        
        private func animateText() {
            guard currentIndex < text.count else { return }
            
            let index = text.index(text.startIndex, offsetBy: currentIndex)
            animatedText += String(text[index])
            currentIndex += 1
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                animateText()
            }
        }
    }
    
    /// Sparkles animation effect
    struct SparklesOverlay: View {
        @State private var sparkles: [Sparkle] = []
        let isActive: Bool
        
        var body: some View {
            ZStack {
                ForEach(sparkles) { sparkle in
                    Image(systemName: "sparkle")
                        .font(.system(size: sparkle.size, weight: .light))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.yellow, .orange, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .position(sparkle.position)
                        .opacity(sparkle.opacity)
                        .scaleEffect(sparkle.scale)
                        .animation(
                            .easeInOut(duration: sparkle.duration)
                            .repeatCount(1, autoreverses: false),
                            value: sparkle.opacity
                        )
                }
            }
            .onAppear {
                if isActive {
                    generateSparkles()
                }
            }
            .onChange(of: isActive) { _, newValue in
                if newValue {
                    generateSparkles()
                } else {
                    sparkles.removeAll()
                }
            }
        }
        
        private func generateSparkles() {
            for _ in 0..<8 {
                let sparkle = Sparkle(
                    position: CGPoint(
                        x: CGFloat.random(in: 0...300),
                        y: CGFloat.random(in: 0...300)
                    ),
                    size: CGFloat.random(in: 8...16),
                    opacity: 1.0,
                    scale: 1.0,
                    duration: Double.random(in: 0.5...1.5)
                )
                sparkles.append(sparkle)
                
                // Animate sparkle
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if let index = sparkles.firstIndex(where: { $0.id == sparkle.id }) {
                        sparkles[index].opacity = 0
                        sparkles[index].scale = 0.5
                    }
                }
                
                // Remove sparkle after animation
                DispatchQueue.main.asyncAfter(deadline: .now() + sparkle.duration + 0.1) {
                    sparkles.removeAll { $0.id == sparkle.id }
                }
            }
        }
    }
    
    /// Breathing animation effect
    struct BreathingEffect: ViewModifier {
        @State private var isBreathing = false
        let duration: Double
        let scaleRange: ClosedRange<CGFloat>
        
        init(duration: Double = 3.0, scaleRange: ClosedRange<CGFloat> = 0.95...1.05) {
            self.duration = duration
            self.scaleRange = scaleRange
        }
        
        func body(content: Content) -> some View {
            content
                .scaleEffect(isBreathing ? scaleRange.upperBound : scaleRange.lowerBound)
                .animation(
                    .easeInOut(duration: duration)
                    .repeatForever(autoreverses: true),
                    value: isBreathing
                )
                .onAppear {
                    isBreathing = true
                }
        }
    }
    
    /// Magnetic hover effect
    struct MagneticHover: ViewModifier {
        @State private var offset: CGSize = .zero
        @State private var isHovered = false
        let strength: CGFloat
        
        init(strength: CGFloat = 10) {
            self.strength = strength
        }
        
        func body(content: Content) -> some View {
            content
                .offset(offset)
                .scaleEffect(isHovered ? 1.05 : 1.0)
                .shadow(
                    color: .white.opacity(isHovered ? 0.2 : 0),
                    radius: isHovered ? 20 : 0,
                    x: 0,
                    y: isHovered ? 10 : 0
                )
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: offset)
                .animation(.easeInOut(duration: 0.3), value: isHovered)
                .onHover { hovering in
                    isHovered = hovering
                    if !hovering {
                        offset = .zero
                    }
                }
                .gesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { value in
                            let distance = sqrt(pow(value.translation.x, 2) + pow(value.translation.y, 2))
                            let normalizedDistance = min(distance / 100, 1)
                            let magneticStrength = strength * (1 - normalizedDistance)
                            
                            offset = CGSize(
                                width: value.translation.x * magneticStrength / 100,
                                height: value.translation.y * magneticStrength / 100
                            )
                        }
                        .onEnded { _ in
                            offset = .zero
                        }
                )
        }
    }
    
    /// Ripple effect for button interactions
    struct RippleEffect: ViewModifier {
        @State private var ripples: [Ripple] = []
        
        func body(content: Content) -> some View {
            content
                .overlay(
                    ZStack {
                        ForEach(ripples) { ripple in
                            Circle()
                                .stroke(Color.white.opacity(0.5), lineWidth: 2)
                                .frame(width: ripple.size, height: ripple.size)
                                .position(ripple.position)
                                .opacity(ripple.opacity)
                                .scaleEffect(ripple.scale)
                                .animation(
                                    .easeOut(duration: 0.6),
                                    value: ripple.scale
                                )
                        }
                    }
                    .clipped()
                )
                .onTapGesture { location in
                    addRipple(at: location)
                }
        }
        
        private func addRipple(at location: CGPoint) {
            let ripple = Ripple(
                position: location,
                size: 0,
                opacity: 1.0,
                scale: 0
            )
            ripples.append(ripple)
            
            // Animate ripple
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                if let index = ripples.firstIndex(where: { $0.id == ripple.id }) {
                    ripples[index].scale = 1.0
                    ripples[index].size = 200
                    ripples[index].opacity = 0
                }
            }
            
            // Remove ripple after animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                ripples.removeAll { $0.id == ripple.id }
            }
        }
    }
}

// MARK: - Data Structures

struct Sparkle: Identifiable {
    let id = UUID()
    let position: CGPoint
    let size: CGFloat
    var opacity: Double
    var scale: CGFloat
    let duration: Double
}

struct Ripple: Identifiable {
    let id = UUID()
    let position: CGPoint
    var size: CGFloat
    var opacity: Double
    var scale: CGFloat
}

// MARK: - View Extensions

extension View {
    /// Apply shimmer loading effect
    func shimmer(duration: Double = 2.0, bounce: Bool = false) -> some View {
        modifier(AnimationHelpers.ShimmerEffect(duration: duration, bounce: bounce))
    }
    
    /// Apply blur fade transition
    func blurFade(isVisible: Bool, duration: Double = 0.6, delay: Double = 0) -> some View {
        modifier(AnimationHelpers.BlurFade(isVisible: isVisible, duration: duration, delay: delay))
    }
    
    /// Apply breathing animation
    func breathing(duration: Double = 3.0, scaleRange: ClosedRange<CGFloat> = 0.95...1.05) -> some View {
        modifier(AnimationHelpers.BreathingEffect(duration: duration, scaleRange: scaleRange))
    }
    
    /// Apply magnetic hover effect
    func magneticHover(strength: CGFloat = 10) -> some View {
        modifier(AnimationHelpers.MagneticHover(strength: strength))
    }
    
    /// Apply ripple effect
    func rippleEffect() -> some View {
        modifier(AnimationHelpers.RippleEffect())
    }
    
    /// Apply sparkles overlay
    func sparkles(isActive: Bool) -> some View {
        overlay(
            AnimationHelpers.SparklesOverlay(isActive: isActive)
        )
    }
}

/// Loading skeleton view
struct LoadingSkeleton: View {
    let height: CGFloat
    let cornerRadius: CGFloat
    
    init(height: CGFloat = 20, cornerRadius: CGFloat = 4) {
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        Rectangle()
            .fill(Color.white.opacity(0.1))
            .frame(height: height)
            .cornerRadius(cornerRadius)
            .shimmer()
    }
}

/// Staggered animation container
struct StaggeredAnimation<Content: View>: View {
    let content: Content
    let stagger: Double
    @State private var isVisible = false
    
    init(stagger: Double = 0.1, @ViewBuilder content: () -> Content) {
        self.stagger = stagger
        self.content = content()
    }
    
    var body: some View {
        content
            .blurFade(isVisible: isVisible)
            .onAppear {
                withAnimation(.easeOut(duration: 0.6).delay(stagger)) {
                    isVisible = true
                }
            }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Shimmer effect
        Rectangle()
            .fill(Color.white.opacity(0.1))
            .frame(height: 60)
            .cornerRadius(12)
            .shimmer()
        
        // Breathing effect
        Circle()
            .fill(Color.cyan.opacity(0.3))
            .frame(width: 80, height: 80)
            .breathing()
        
        // Morphing text
        AnimationHelpers.MorphingText(
            "Session Quality: Excellent",
            font: .system(size: 18, weight: .semibold),
            color: .white
        )
        
        // Magnetic hover
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.blue.opacity(0.3))
            .frame(width: 200, height: 60)
            .magneticHover()
        
        // Ripple effect
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.green.opacity(0.3))
            .frame(width: 200, height: 60)
            .rippleEffect()
    }
    .padding()
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
