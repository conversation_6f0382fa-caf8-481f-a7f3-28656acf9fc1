//
//  AnalyticsView.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI
import Charts

/// Analytics view model
@MainActor
class AnalyticsViewModel: ObservableObject {
    @Published var qualityData: [AnalyticsDataPoint] = []
    @Published var adherenceData: [AnalyticsDataPoint] = []
    @Published var pressureData: [AnalyticsDataPoint] = []
    @Published var durationData: [AnalyticsDataPoint] = []
    @Published var patternsData: [AnalyticsDataPoint] = []
    @Published var keyInsights: [KeyInsight] = []
    @Published var recommendations: [Recommendation] = []

    func loadAnalytics(timeframe: AnalyticsView.AnalyticsTimeframe) {
        // Load analytics data based on timeframe
        // This is a placeholder implementation
        generateMockData(for: timeframe)
        generateInsights()
        generateRecommendations()
    }

    func getInsightValue(for insight: AnalyticsView.AnalyticsInsight) -> String {
        switch insight {
        case .quality: return "85%"
        case .adherence: return "92%"
        case .pressure: return "15.2"
        case .duration: return "12m"
        case .patterns: return "+18%"
        }
    }

    private func generateMockData(for timeframe: AnalyticsView.AnalyticsTimeframe) {
        let calendar = Calendar.current
        let now = Date()
        let dayCount: Int

        switch timeframe {
        case .week: dayCount = 7
        case .month: dayCount = 30
        case .quarter: dayCount = 90
        case .year: dayCount = 365
        }

        qualityData = (0..<dayCount).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { return nil }
            return AnalyticsDataPoint(date: date, value: Double.random(in: 70...95))
        }.reversed()

        adherenceData = (0..<dayCount).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { return nil }
            return AnalyticsDataPoint(date: date, value: Double.random(in: 60...100))
        }.reversed()

        pressureData = (0..<dayCount).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { return nil }
            return AnalyticsDataPoint(date: date, value: Double.random(in: 10...20))
        }.reversed()

        durationData = (0..<dayCount).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { return nil }
            return AnalyticsDataPoint(date: date, value: Double.random(in: 8...15))
        }.reversed()

        patternsData = qualityData // Use quality data for patterns
    }

    private func generateInsights() {
        keyInsights = [
            KeyInsight(
                title: "Quality Improving",
                description: "Your session quality has improved by 12% this month",
                icon: "arrow.up.right",
                color: .green,
                trend: "+12%",
                trendColor: .green
            ),
            KeyInsight(
                title: "Consistent Pressure",
                description: "You're maintaining optimal pressure zones 85% of the time",
                icon: "gauge.high",
                color: .orange,
                trend: "85%",
                trendColor: .orange
            ),
            KeyInsight(
                title: "Strong Adherence",
                description: "You've completed 92% of planned sessions this month",
                icon: "calendar.badge.checkmark",
                color: .green,
                trend: "92%",
                trendColor: .green
            ),
            KeyInsight(
                title: "Optimal Duration",
                description: "Average session duration is within recommended range",
                icon: "clock.fill",
                color: .blue,
                trend: "12m",
                trendColor: .blue
            )
        ]
    }

    private func generateRecommendations() {
        recommendations = [
            Recommendation(
                title: "Extend Morning Sessions",
                description: "Your morning sessions show 15% better quality. Consider scheduling more sessions in the morning.",
                priority: .high
            ),
            Recommendation(
                title: "Focus on Pressure Control",
                description: "Work on maintaining consistent pressure in the green zone for better outcomes.",
                priority: .medium
            ),
            Recommendation(
                title: "Set Weekly Reminders",
                description: "You tend to miss sessions on weekends. Set up reminders to maintain consistency.",
                priority: .low
            )
        ]
    }
}

/// Analytics data point
struct AnalyticsDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
}

/// Key insight data structure
struct KeyInsight: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let icon: String
    let color: Color
    let trend: String
    let trendColor: Color
}

/// Recommendation data structure
struct Recommendation: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let priority: Priority

    enum Priority {
        case high, medium, low

        var color: Color {
            switch self {
            case .high: return .red
            case .medium: return .orange
            case .low: return .blue
            }
        }
    }
}

/// Advanced analytics view with detailed insights and patterns
struct AnalyticsView: View {
    @StateObject private var viewModel = AnalyticsViewModel()
    @State private var selectedTimeframe: AnalyticsTimeframe = .month
    @State private var selectedInsight: AnalyticsInsight = .quality
    
    enum AnalyticsTimeframe: String, CaseIterable {
        case week = "Week"
        case month = "Month"
        case quarter = "Quarter"
        case year = "Year"
    }
    
    enum AnalyticsInsight: String, CaseIterable {
        case quality = "Quality"
        case adherence = "Adherence"
        case pressure = "Pressure"
        case duration = "Duration"
        case patterns = "Patterns"
        
        var icon: String {
            switch self {
            case .quality: return "star.fill"
            case .adherence: return "calendar.badge.checkmark"
            case .pressure: return "gauge.high"
            case .duration: return "clock.fill"
            case .patterns: return "chart.line.uptrend.xyaxis"
            }
        }
        
        var color: Color {
            switch self {
            case .quality: return .cyan
            case .adherence: return .green
            case .pressure: return .orange
            case .duration: return .purple
            case .patterns: return .pink
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Timeframe selector
                    timeframeSelectorSection
                    
                    // Insight selector
                    insightSelectorSection
                    
                    // Main analytics content
                    analyticsContentSection
                    
                    // Key insights summary
                    insightsSummarySection
                    
                    // Recommendations
                    recommendationsSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .background(backgroundGradient)
            .navigationBarHidden(true)
        }
        .onAppear {
            viewModel.loadAnalytics(timeframe: selectedTimeframe)
        }
        .onChange(of: selectedTimeframe) { _, newValue in
            viewModel.loadAnalytics(timeframe: newValue)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            Button(action: {
                // Navigate back
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Analytics")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("Insights from your therapy sessions")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
        }
        .padding(.top, 20)
    }
    
    // MARK: - Timeframe Selector
    
    private var timeframeSelectorSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AnalyticsTimeframe.allCases, id: \.self) { timeframe in
                    TimeframeChip(
                        title: timeframe.rawValue,
                        isSelected: selectedTimeframe == timeframe
                    ) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTimeframe = timeframe
                        }
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - Insight Selector
    
    private var insightSelectorSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                ForEach(AnalyticsInsight.allCases, id: \.self) { insight in
                    InsightCard(
                        insight: insight,
                        isSelected: selectedInsight == insight,
                        value: viewModel.getInsightValue(for: insight)
                    ) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedInsight = insight
                        }
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - Analytics Content
    
    private var analyticsContentSection: some View {
        VStack(spacing: 20) {
            switch selectedInsight {
            case .quality:
                QualityAnalyticsView(
                    data: viewModel.qualityData,
                    timeframe: selectedTimeframe
                )
                
            case .adherence:
                AdherenceAnalyticsView(
                    data: viewModel.adherenceData,
                    timeframe: selectedTimeframe
                )
                
            case .pressure:
                PressureAnalyticsView(
                    data: viewModel.pressureData,
                    timeframe: selectedTimeframe
                )
                
            case .duration:
                DurationAnalyticsView(
                    data: viewModel.durationData,
                    timeframe: selectedTimeframe
                )
                
            case .patterns:
                PatternsAnalyticsView(
                    data: viewModel.patternsData,
                    timeframe: selectedTimeframe
                )
            }
        }
    }
    
    // MARK: - Insights Summary
    
    private var insightsSummarySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Key Insights")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(viewModel.keyInsights, id: \.id) { insight in
                    InsightSummaryCard(insight: insight)
                }
            }
        }
    }
    
    // MARK: - Recommendations
    
    private var recommendationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommendations")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            VStack(spacing: 12) {
                ForEach(viewModel.recommendations, id: \.id) { recommendation in
                    RecommendationCard(recommendation: recommendation)
                }
            }
        }
    }
    
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
}

/// Timeframe selection chip
struct TimeframeChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(isSelected ? .black : .white.opacity(0.8))
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    Capsule()
                        .fill(isSelected ? Color.white : Color.white.opacity(0.1))
                        .overlay(
                            Capsule()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .scaleEffect(isSelected ? 1.05 : 1.0)
                .shadow(
                    color: isSelected ? Color.white.opacity(0.2) : .clear,
                    radius: isSelected ? 8 : 0,
                    x: 0,
                    y: isSelected ? 4 : 0
                )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: isSelected)
    }
}

/// Insight selection card
struct InsightCard: View {
    let insight: AnalyticsView.AnalyticsInsight
    let isSelected: Bool
    let value: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Icon
                ZStack {
                    Circle()
                        .fill(insight.color.opacity(isSelected ? 0.3 : 0.15))
                        .frame(width: 50, height: 50)
                        .scaleEffect(isSelected ? 1.1 : 1.0)
                    
                    Image(systemName: insight.icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(insight.color)
                }
                
                // Value
                Text(value)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.white.opacity(0.9))
                    .contentTransition(.numericText())
                
                // Title
                Text(insight.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            .frame(width: 120, height: 120)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(isSelected ? 0.1 : 0.05),
                                Color.white.opacity(isSelected ? 0.05 : 0.02)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                insight.color.opacity(isSelected ? 0.5 : 0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
                    .shadow(
                        color: insight.color.opacity(isSelected ? 0.3 : 0.1),
                        radius: isSelected ? 15 : 8,
                        x: 0,
                        y: isSelected ? 8 : 4
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: isSelected)
    }
}

/// Insight summary card
struct InsightSummaryCard: View {
    let insight: KeyInsight
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: insight.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(insight.color)
                
                Spacer()
                
                Text(insight.trend)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(insight.trendColor)
            }
            
            Text(insight.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(2)
            
            Text(insight.description)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
                .lineLimit(3)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(insight.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

/// Recommendation card
struct RecommendationCard: View {
    let recommendation: Recommendation
    
    var body: some View {
        HStack(spacing: 16) {
            // Priority indicator
            Circle()
                .fill(recommendation.priority.color)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 6) {
                Text(recommendation.title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Text(recommendation.description)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(3)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.4))
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(recommendation.priority.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    AnalyticsView()
}
